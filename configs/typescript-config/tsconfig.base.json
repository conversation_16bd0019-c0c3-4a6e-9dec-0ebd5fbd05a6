{"compilerOptions": {"noEmit": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "allowImportingTsExtensions": true, "jsx": "preserve", "moduleDetection": "force", "useDefineForClassFields": true, "noImplicitThis": true, "strict": true, "isolatedModules": true, "verbatimModuleSyntax": false, "target": "ESNext", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}}