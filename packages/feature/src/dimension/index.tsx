import { defineComponent } from 'vue';

const Button = defineComponent({
  name: 'ProButton',
  emits: ['click'],
  props: {
    type: {
      type: String,
      default: 'primary',
    },
  },
  setup(props, { emit }) {
    const onClick = () => {
      emit('click');
    };
    return {
      onClick,
    };
  },
  render() {
    return (
      <div class="pro-button" data-type={this.type} onClick={this.onClick}>
        <div>
          {this.$slots.default} ({this.type})
        </div>
        <div>XXXX</div>
      </div>
    );
  },
});

export default Button;
