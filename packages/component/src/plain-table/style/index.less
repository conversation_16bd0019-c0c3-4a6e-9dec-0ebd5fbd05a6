@import (reference) "../../style/token.less";
@import "../../style/mixin.less";

@plain-table-class: ~'@{prefix}-plain-table';

@plain-table-column-gap: 9px 10px;
@plain-table-header-bg: #f2f8fe;
@plain-table-border-color: #e4eef6;
@plain-table-th-padding: @plain-table-column-gap;
@plain-table-td-padding: @plain-table-column-gap;
@plain-table-font-size: inherit;

.@{plain-table-class} {
  width: 100%;
  margin: 0 auto;

  &__table {
    width: 100%;
    color: #333;
    border-spacing: 0;
    line-height: 1.5;
    border-collapse: collapse;
    font-size: @plain-table-font-size;

    th,
    td {
      border: 1px solid @plain-table-border-color;
    }

    th {
      padding: @plain-table-th-padding;
      background: @plain-table-header-bg;
      font-weight: normal;
      line-height: 20px;
      white-space: nowrap;
    }

    td {
      padding: @plain-table-td-padding;
      word-break: break-word;
      word-wrap: break-word;

      &:not([data-blank]) {
        &:empty::before {
          content: '-';
        }
      }
    }

    :global {
      .tb {
        background: @plain-table-header-bg !important;
      }

      [align='center'] {
        text-align: center;
        vertical-align: middle;
      }
    }
  }
}
