import { mount, shallowMount, Wrapper } from '@vue/test-utils';
import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import PlainTable from '..';
import Vue, { ComponentPublicInstance } from 'vue';

describe('PlainTable', () => {
  // let wrapper: Wrapper<InstanceType<typeof PlainTable>>;
  // let wrapper: Wrapper<InstanceType<typeof PlainTable>>;
  let wrapper: Wrapper<ComponentPublicInstance<any>>;

  describe('基础渲染', () => {
    test('应该正确渲染组件', () => {
      const wrapper = mount(PlainTable);

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.is('div')).toBe(true);
    });

    test('应该包含正确的CSS类名', () => {
      wrapper = mount(PlainTable);

      expect(wrapper.classes()).toContain('pro-plain-table');

      const table = wrapper.find('table');
      expect(table.exists()).toBe(true);
      expect(table.classes()).toContain('pro-plain-table__table');
    });

    test('应该渲染正确的DOM结构', () => {
      wrapper = mount(PlainTable);

      // 验证外层div
      expect(wrapper.element.tagName).toBe('DIV');
      expect(wrapper.classes()).toContain('pro-plain-table');

      // 验证内层table
      const table = wrapper.find('table');
      expect(table.exists()).toBe(true);
      expect(table.element.tagName).toBe('TABLE');
      expect(table.classes()).toContain('pro-plain-table__table');
    });
  });

  describe('插槽内容', () => {
    test('应该正确渲染children内容', () => {
      const tableContent = `
        <thead>
          <tr>
            <th>列1</th>
            <th>列2</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>数据1</td>
            <td>数据2</td>
          </tr>
        </tbody>
      `;

      wrapper = mount(PlainTable, {
        slots: {
          default: tableContent,
        },
      });

      const table = wrapper.find('table');
      expect(table.find('thead').exists()).toBe(true);
      expect(table.find('tbody').exists()).toBe(true);
      expect(table.find('th').text()).toBe('列1');
      expect(table.find('td').text()).toBe('数据1');
    });

    test('应该支持空内容', () => {
      wrapper = mount(PlainTable);

      const table = wrapper.find('table');
      expect(table.exists()).toBe(true);
      expect(table.text()).toBe('');
    });

    test('应该支持复杂的表格内容', () => {
      const complexContent = `
        <thead>
          <tr>
            <th rowspan="2">姓名</th>
            <th colspan="2">成绩</th>
          </tr>
          <tr>
            <th>数学</th>
            <th>英语</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>张三</td>
            <td>90</td>
            <td>85</td>
          </tr>
        </tbody>
      `;

      wrapper = mount(PlainTable, {
        slots: {
          default: complexContent,
        },
      });

      const table = wrapper.find('table');
      const thElements = table.findAll('th');
      const tdElements = table.findAll('td');

      expect(thElements).toHaveLength(4);
      expect(tdElements).toHaveLength(3);
      expect(thElements.at(0).text()).toBe('姓名');
      expect(tdElements.at(0).text()).toBe('张三');
    });
  });

  describe('组件属性', () => {
    test('应该有正确的组件名称', () => {
      expect(PlainTable.name).toBe('ProPlainTable');
    });

    test('应该是函数式组件', () => {
      expect(PlainTable.functional).toBe(true);
    });
  });

  describe('快照测试', () => {
    test('空内容快照', () => {
      wrapper = mount(PlainTable);
      expect(wrapper.html()).toMatchSnapshot();
    });

    test('带内容快照', () => {
      const content = `
        <thead>
          <tr>
            <th>标题1</th>
            <th>标题2</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>内容1</td>
            <td>内容2</td>
          </tr>
        </tbody>
      `;

      wrapper = mount(PlainTable, {
        slots: {
          default: content,
        },
      });

      expect(wrapper.html()).toMatchSnapshot();
    });
  });
});
