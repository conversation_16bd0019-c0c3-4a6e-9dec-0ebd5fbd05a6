{"name": "@pro-ui/component", "version": "0.1.0", "author": {"email": "<EMAIL>", "name": "Yandongxu"}, "scripts": {"dev": "gulp dev", "build": "gulp build", "test:unit": "vitest"}, "publishConfig": {"registry": "http://verdacciokzz.dev.greatld.com"}, "type": "module", "module": "dist/esm/index.js", "types": "dist/types/index.d.ts", "exports": {".": {"import": "./dist/esm/index.js", "types": "./dist/types/index.d.ts"}, "./*": {"import": "./dist/esm/*"}}, "files": ["dist"], "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@microsoft/api-extractor": "^7.52.8", "@pro-ui/typescript-config": "workspace:*", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-typescript": "^12.1.2", "@rollup/plugin-url": "^8.0.2", "@types/node": "^22.0.0", "@vitejs/plugin-vue2": "^2.3.3", "@vitejs/plugin-vue2-jsx": "^1.1.1", "@vitest/coverage-v8": "^3.2.3", "@vitest/ui": "^3.2.3", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/babel-preset-jsx": "^1.4.0", "@vue/test-utils": "^1.3.6", "fake-indexeddb": "^6.0.1", "glob": "^11.0.2", "gulp": "^5.0.1", "gulp-clean": "^0.4.0", "gulp-less": "^5.0.0", "happy-dom": "^18.0.1", "jest-serializer-vue": "^3.1.0", "lodash": "^4.17.21", "moment": "^2.30.1", "postcss-url": "^10.1.3", "rollup": "^4.42.0", "rollup-plugin-delete": "^3.0.1", "rollup-plugin-dts": "^6.2.1", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "tslib": "^2.8.1", "typescript": "^5.8.3", "vite": "^5.4.19", "vitest": "^3.2.3", "vitest-sonar-reporter": "^2.0.1", "vue": "^2.7.16"}, "peerDependencies": {"@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "ant-design-vue": "^1.7.8", "less": "^4.2.2", "lodash": "^4.17.21", "moment": "^2.30.1", "vue": "^2.7.16"}}