import { defineConfig, mergeConfig, coverageConfigDefaults } from 'vitest/config';
import viteConfig from './vite.config';
import { fileURLToPath } from 'node:url';

export default defineConfig((configEnv) =>
  mergeConfig(
    viteConfig(configEnv),
    defineConfig({
      define: {
        __VUE_PROD_DEVTOOLS__: false, // 禁用 DevTools 提示
        // 使用默认环境变量, 在执行测试时为 'test', ant-design-vue 会读取该环境变量来控制是否生成 aria-controls=UUID, 解决快照
        // https://github.com/vueComponent/ant-design-vue/blob/acaad271351ca0966f828fd323ecfa05cefe6537/components/vc-select/util.js#L212
        // 'process.env.NODE_ENV': JSON.stringify('production'),
      },
      test: {
        css: {
          modules: {
            classNameStrategy: 'non-scoped',
          },
        },
        exclude: ['**/node_modules/**'],
        reporters: [
          'default',
          [
            'html',
            {
              outputFile: './test-report/index.html',
            },
          ],
          [
            'vitest-sonar-reporter',
            {
              outputFile: './test-report/sonar-reporter.xml',
            },
          ],
        ],
        coverage: {
          all: true,
          enabled: false, // or '--coverage.enabled'
          clean: false, // or '--coverage.clean'
          reporter: ['text', 'lcovonly', 'html'],
          reportsDirectory: './test-report/coverage',
          provider: 'v8',
          include: ['src/**/*.{ts,tsx,vue,js,jsx}'],
          exclude: [...coverageConfigDefaults.exclude, '**/node_modules/**'],
        },
        setupFiles: [fileURLToPath(new URL('./vitest.setup.ts', import.meta.url))],
        environment: 'happy-dom',
        globals: true,
      },
    })
  )
);
