import Vue from 'vue';
import { Modal } from 'ant-design-vue';
import { expect } from 'vitest';
import { config } from '@vue/test-utils';
import vueSnapshotSerializer from 'jest-serializer-vue';
import 'fake-indexeddb/auto';

expect.addSnapshotSerializer(vueSnapshotSerializer);

const setup = () => {
  // -------------------------------------
  // Vue test util setup
  // -------------------------------------
  config.showDeprecationWarnings = false;
  config.stubs = {};
  config.mocks = {};

  // -------------------------------------
  // Vue setup
  // -------------------------------------
  Vue.config.silent = true;
  Vue.config.productionTip = false;
  Vue.config.devtools = false;
  Vue.use(Modal); // AntPortal directive

  // -------------------------------------
  // Jest mock setup
  // -------------------------------------
  // vi.mock('vue-router/composables', () => {
  //   const promisifyFn = () => vi.fn().mockResolvedValue(undefined);
  //   const mockRoute = {
  //     url: '',
  //     path: '',
  //     hash: '',
  //     name: '',
  //     query: {},
  //     params: {},
  //     fullPath: '',
  //     matched: [],
  //     meta: {},
  //   };
  //   const mockRouter = {
  //     options: {
  //       routes: [],
  //     },
  //     push: promisifyFn(),
  //     replace: promisifyFn(),
  //     back: promisifyFn(),
  //     go: promisifyFn(),
  //     forward: promisifyFn(),
  //     resolve: promisifyFn(),
  //     catch: vi.fn(),
  //     currentRoute: mockRoute,
  //   };
  //   return {
  //     useRouter: () => {
  //       return mockRouter;
  //     },
  //     useRoute: () => {
  //       return mockRoute;
  //     },
  //   };
  // });
};

setup();
