import { fileURLToPath, URL } from 'node:url';
import { AliasOptions, defineConfig, Plugin, type PluginOption } from 'vite';
import vue from '@vitejs/plugin-vue2';
import jsx from '@vitejs/plugin-vue2-jsx';
import { version as APP_VERSION } from './package.json';

const ALIAS: AliasOptions = {
  '@': fileURLToPath(new URL('./src', import.meta.url)),
};

export default defineConfig(({ mode }) => {
  const plugins: Plugin[] = [vue(), jsx()];

  return {
    // base: '/',
    // build: {
    //   target: 'es2015',
    //   rollupOptions: {
    //     input: 'src/index.ts',
    //   },
    //   commonjsOptions: {
    //     transformMixedEsModules: true,
    //   },
    // },
    define: {
      'process.env.NODE_ENV': JSON.stringify(mode),
      'import.meta.env.APP_VERSION': JSON.stringify(APP_VERSION),
    },
    plugins,
    resolve: {
      alias: ALIAS,
      extensions: ['.mjs', '.js', '.mts', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    },
    css: {
      modules: {
        localsConvention: 'camelCaseOnly',
        generateScopedName: '[name]__[local]_[hash:base64:5]',
      },
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          math: 'always',
        },
      },
    },
  };
});
