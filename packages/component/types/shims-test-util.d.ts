import { ComponentPublicInstance } from 'vue';
import { ThisTypedMountOptions } from '@vue/test-utils';

declare module '@vue/test-utils' {
  export function mount<V extends object>(
    originalComponent: {
      new (...args: any[]): V;
    },
    options?: ThisTypedMountOptions<V>
  ): Wrapper<ComponentPublicInstance<V>>;

  export function shallowMount<V extends object>(
    originalComponent: {
      new (...args: any[]): V;
    },
    options?: ThisTypedMountOptions<V>
  ): Wrapper<ComponentPublicInstance<V>>;
}
