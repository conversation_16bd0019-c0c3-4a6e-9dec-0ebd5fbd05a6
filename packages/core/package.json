{"name": "@pro-ui/core", "version": "0.1.0", "author": {"email": "<EMAIL>", "name": "Yandongxu"}, "scripts": {"build": "rollup -c"}, "publishConfig": {"registry": "http://verdacciokzz.dev.greatld.com"}, "type": "module", "module": "dist/esm/index.js", "types": "dist/types/index.d.ts", "exports": {".": {"import": "./dist/esm/index.js", "types": "./dist/types/index.d.ts"}}, "files": ["dist"], "devDependencies": {"@microsoft/api-extractor": "^7.52.8", "@pro-ui/typescript-config": "workspace:*", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-typescript": "^12.1.2", "@vue/test-utils": "^1.3.6", "@types/node": "^22.0.0", "@vitejs/plugin-vue2": "^2.3.3", "@vitejs/plugin-vue2-jsx": "^1.1.1", "@vitest/coverage-v8": "^3.2.3", "@vitest/ui": "^3.2.3", "glob": "^11.0.2", "jest-serializer-vue": "^3.1.0", "rollup": "^4.43.0", "rollup-plugin-delete": "^3.0.1", "rollup-plugin-dts": "^6.2.1", "tslib": "^2.8.1", "typescript": "^5.8.3", "vite": "^5.4.19", "vitest": "^3.2.3", "vitest-sonar-reporter": "^2.0.1"}}