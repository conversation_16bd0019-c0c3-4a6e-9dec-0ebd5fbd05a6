import resolve from '@rollup/plugin-node-resolve';
import typescript from '@rollup/plugin-typescript';
import { globSync } from 'glob';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import { defineConfig } from 'rollup';
import dts from "rollup-plugin-dts";
import remove from 'rollup-plugin-delete';

const extensions = ['.js', '.jsx', '.ts', '.tsx', '.vue'];

const files = Object.fromEntries(
  globSync('src/**/*.{ts,tsx}').map((file) => [
    path.relative('src', file.slice(0, file.length - path.extname(file).length)),
    fileURLToPath(new URL(file, import.meta.url)),
  ])
);

const esm = defineConfig({
  plugins: [
    remove({ targets: 'dist/esm' }),
    resolve({ extensions }),
    typescript({
      tsconfig: './tsconfig.prod.json',
      compilerOptions: {
        allowImportingTsExtensions: false,
        noEmit: true,
      },
    }),
  ],
  input: files,
  output: {
    dir: 'dist/esm',
    format: 'esm',
  },
});

const types = defineConfig({
  plugins: [
    remove({ targets: 'dist/types' }),
    resolve({ extensions }),
    dts({
      tsconfig: './tsconfig.prod.json',
      respectExternal: true,
      compilerOptions: {
        declaration: true,
        emitDeclarationOnly: true,
      },
    }),
  ],
  input: files,
  output: {
    dir: 'dist/types',
    format: 'esm',
  },
})

export default [esm, types];
