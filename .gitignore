# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
node_modules
.pnp
.pnp.js

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage/
test-report/

# Turbo
.turbo

# Build Outputs
dist/
out/


# Debug
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Misc
.DS_Store
*.pem
.temp
.tmp
