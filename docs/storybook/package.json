{"name": "@qcc-ui/storybook", "version": "0.0.0", "private": "true", "author": {"email": "<EMAIL>", "name": "Yandongxu"}, "scripts": {"dev": "storybook dev -p 6006", "build": "storybook build -o dist/", "lint:style": "stylelint \"**/*.{css,less,scss,sass,html,vue}\" --fix --ignore-path .gitignore", "lint:js": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint": "run-p format lint:*"}, "dependencies": {"@pro-ui/component": "workspace:*", "ant-design-vue": "^1.7.8", "vue": "^2.7.16"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@pro-ui/typescript-config": "workspace:*", "@storybook/addon-actions": "~7.6.20", "@storybook/addon-essentials": "~7.6.20", "@storybook/addon-interactions": "~7.6.20", "@storybook/addon-links": "~7.6.20", "@storybook/blocks": "~7.6.20", "@storybook/manager-api": "~7.6.20", "@storybook/test": "~7.6.20", "@storybook/theming": "~7.6.20", "@storybook/vue": "~7.6.20", "@storybook/vue-vite": "~7.6.20", "@types/node": "^18.0.0", "@vitejs/plugin-legacy": "^5.0.0", "@vitejs/plugin-vue2": "^2.3.3", "@vitejs/plugin-vue2-jsx": "^1.1.1", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^1.3.6", "@vue/tsconfig": "^0.1.3", "eslint": "^8.55.0", "eslint-plugin-storybook": "^0.11.6", "eslint-plugin-vue": "^9.19.2", "less": "^4.2.2", "npm-run-all2": "^6.1.1", "postcss-html": "^1.5.0", "prettier": "^3.1.0", "react": "~18.2.0", "react-dom": "~18.2.0", "storybook": "~7.6.20", "stylelint": "^16.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "terser": "^5.24.0", "typescript": "~5.1.0", "vite": "^5.0.0", "vue-template-compiler": "^2.7.16", "vue-tsc": "^1.8.25"}}