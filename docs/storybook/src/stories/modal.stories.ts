import { ConfigProvider } from 'ant-design-vue';
import type { Meta, StoryObj } from '@storybook/vue';
import { userEvent, within } from '@storybook/test';
import { action } from '@storybook/addon-actions';
import { Modal } from '@pro-ui/component';

const meta: Meta<typeof Modal> = {
  component: Modal,
  title: 'Components/Modal 卡片',
  tags: ['autodocs'],
  decorators: [
    (story) => ({
      components: { story, ConfigProvider },
      template: '<ConfigProvider :autoInsertSpaceInButton="false"><story /></ConfigProvider>',
    }),
  ],
  argTypes: {
    title: {
      control: 'text',
      description: '卡片标题',
      table: {
        category: 'Props',
      },
    },
    onClick: {
      table: {
        category: 'Events',
      },
      // 设置 action 默认值
      action: 'onClicked',
    },
  },
  args: {
    title: '默认卡片标题',
  },
  render: (args, { argTypes }) => ({
    components: { Modal },
    props: Object.keys(argTypes),
    template: '<Modal v-bind="$props" @click="onClick" />',
  }),
};

export default meta;

type Story = StoryObj<typeof Modal>;

/**
 * More on writing stories with args: https://storybook.js.org/docs/vue/writing-stories/args
 */
export const Default: Story = {
  args: {
    onClick: action('Override click action'),
  },
};

/**
 * Action story：自定义 action
 */
export const ActionDemo: Story = {
  args: {
    title: 'ActionDemo',
  },
  render: (args, { argTypes }) => ({
    components: { Modal },
    props: Object.keys(argTypes),
    setup() {
      return {
        onClick: action('Override click action'), // 自定义 action
      };
    },
    template: '<Modal v-bind="$props" @click="onClick" />',
  }),
};

/**
 * Play story：结合
 */
export const PlayDemo: Story = {
  args: {
    title: 'PlayDemo',
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByText('PlayDemo'));
  },
};
