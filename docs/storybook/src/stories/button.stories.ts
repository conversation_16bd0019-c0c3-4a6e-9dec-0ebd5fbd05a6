import { ConfigProvider } from 'ant-design-vue';
import type { Meta, StoryObj } from '@storybook/vue';
import { action } from '@storybook/addon-actions';
import { Button } from '@pro-ui/component';

type ButtonType = Partial<typeof Button> & {
  onClick: () => void;
};

const meta: Meta<ButtonType> = {
  component: Button,
  title: 'Components/Button 按钮',
  tags: ['autodocs'],
  decorators: [
    (story) => ({
      components: { story, ConfigProvider },
      template: '<story />',
    }),
  ],
  argTypes: {
    type: {
      control: 'text',
      description: '按钮类型',
      table: {
        category: 'Props',
      },
    },
    onClick: {
      table: {
        category: 'Events',
      },
      // 设置 action 默认值
      action: 'onClicked',
    },
  },
  args: {
    type: 'primary',
  },
  render: (args, { argTypes }) => ({
    components: { Button },
    props: Object.keys(argTypes),
    template: '<Button v-bind="$props" @click="onClick">按钮</Button>',
  }),
};

export default meta;

type Story = StoryObj<ButtonType>;

/**
 * More on writing stories with args: https://storybook.js.org/docs/vue/writing-stories/args
 */
export const Default: Story = {
  args: {
    onClick: action('Override click action'),
  },
};
