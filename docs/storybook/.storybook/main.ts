import type { StorybookConfig } from '@storybook/vue-vite';

const config: StorybookConfig = {
  framework: {
    name: '@storybook/vue-vite',
    options: {},
  },
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: ['@storybook/addon-essentials', '@storybook/addon-links', '@storybook/addon-interactions'],
  core: {
    disableTelemetry: true,
  },
  docs: {
    autodocs: 'tag',
  },
  // https://storybook.js.org/docs/api/main-config/main-config-vite-final
  viteFinal: async (config, { configType }) => {
    const { mergeConfig } = await import('vite');
    if (configType === 'DEVELOPMENT') {
      // Your development configuration goes here
    }
    if (configType === 'PRODUCTION') {
      // Your production configuration goes here.
      return mergeConfig(config, {
        build: {
          commonjsOptions: {
            transformMixedEsModules: true, // 支持混合模块: 包中同时包含 ES 模块和 CommonJS 模块
          },
        },
      });
    }
    return config;
  },
};
export default config;
