import Vue from 'vue';
import { type Preview } from '@storybook/vue';
import PortalDirective from 'ant-design-vue/es/_util/portalDirective';
import { storyTheme } from './theme';
import './preview.less';

Vue.use(PortalDirective);

const parameters: Preview['parameters'] = {
  actions: { argTypesRegex: '^on[A-Z].*' }, // 通过事件名称免配置自动执行 action, e.g., `@onClick`
  controls: {
    matchers: {
      color: /(background|color)$/i,
      date: /Date$/i,
    },
  },
  docs: {
    theme: storyTheme,
  },
};

const preview: Preview = {
  parameters,
  // 全局扩展
  // decorators: [
  //   (story) => ({
  //     components: {
  //       story,
  //       ConfigProvider,
  //     },
  //     // template: '<div style="margin: 3em;"><story /></div>',
  //     template: '<config-provider :autoInsertSpaceInButton="false"><story /></config-provider>',
  //   }),
  // ],
};

export default preview;
